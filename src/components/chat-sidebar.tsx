"use client";

import { useState, useRef, useEffect, useCallback } from "react";
import Image from "next/image";
import { Send, X, Paperclip, Wand2, AtSign, Brain, BookOpen, ChevronDown, MessageCircle, GitBranch, Menu, Plus, Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Toggle } from "@/components/ui/toggle";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";

// Custom N Icon component for NeoChat - same as used in navigation
const NIcon = ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
  <Image
    src="/NeoTask_Icon_N.webp"
    alt="NeoTask Agent"
    width={20}
    height={20}
    className={`${className} object-contain`}
    style={style}
  />
);

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

interface SelectedTag {
  id: string;
  type: 'list' | 'task';
  name: string;
  listName?: string; // For tasks, include the list name
}

interface ChatSidebarProps {
  isOpen: boolean;
  onToggle: () => void;
  width?: number;
  onWidthChange?: (width: number) => void;
}

export function ChatSidebar({
  isOpen,
  onToggle,
  width = 320,
  onWidthChange
}: ChatSidebarProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      text: "Hello! I'm your AI assistant. How can I help you today?",
      isUser: false,
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [isResizing, setIsResizing] = useState(false);

  // New chat controls state
  const [isAutoMode, setIsAutoMode] = useState(true);
  const [isQuestionMode, setIsQuestionMode] = useState(false);
  const [selectedModel, setSelectedModel] = useState("gpt-4");
  const [selectedTags, setSelectedTags] = useState<SelectedTag[]>([]);
  const [isListsMenuOpen, setIsListsMenuOpen] = useState(false);
  const [isMemoriesOpen, setIsMemoriesOpen] = useState(false);
  const [isRulesOpen, setIsRulesOpen] = useState(false);
  const [isConversationsOpen, setIsConversationsOpen] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const sidebarRef = useRef<HTMLDivElement>(null);

  // Resize constraints
  const MIN_WIDTH = 400;
  const MAX_WIDTH = 500;

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Note: Removed auto-focus behavior for better UX on both mobile and desktop

  // Auto-resize textarea
  useEffect(() => {
    const textarea = inputRef.current;
    if (textarea) {
      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = 'auto';
      // Set height to scrollHeight, but constrain within min/max bounds
      const newHeight = Math.min(Math.max(textarea.scrollHeight, 40), 120);
      textarea.style.height = `${newHeight}px`;
    }
  }, [inputValue]);

  // Handle resize functionality
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isResizing || !onWidthChange) return;

    const newWidth = window.innerWidth - e.clientX;
    const clampedWidth = Math.max(MIN_WIDTH, Math.min(MAX_WIDTH, newWidth));
    onWidthChange(clampedWidth);
  }, [isResizing, onWidthChange, MIN_WIDTH, MAX_WIDTH]);

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
  }, []);

  // Add global mouse event listeners for resize
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'ew-resize';
      document.body.style.userSelect = 'none';

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputValue.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue("");
    setIsTyping(true);

    // Simulate AI response with a delay
    setTimeout(() => {
      const responses = [
        "That's an interesting question! Let me think about that.",
        "I understand what you're asking. Here's what I think...",
        "Great point! I can help you with that.",
        "Thanks for sharing that with me. Here's my perspective:",
        "I see what you mean. Let me provide some insights on that.",
        "That's a good observation. Here's how I would approach it:",
      ];

      const randomResponse = responses[Math.floor(Math.random() * responses.length)];

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: randomResponse,
        isUser: false,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, aiMessage]);
      setIsTyping(false);
    }, 1000 + Math.random() * 2000); // Random delay between 1-3 seconds
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Helper functions for tag management
  const addTag = (tag: SelectedTag) => {
    if (!selectedTags.find(t => t.id === tag.id && t.type === tag.type)) {
      setSelectedTags(prev => [...prev, tag]);
    }
  };

  const removeTag = (tagId: string, tagType: 'list' | 'task') => {
    setSelectedTags(prev => prev.filter(t => !(t.id === tagId && t.type === tagType)));
  };

  // Mock data for lists and tasks (replace with actual data)
  const mockLists = [
    { id: '1', name: 'Work Tasks', taskCount: 5 },
    { id: '2', name: 'Personal', taskCount: 3 },
    { id: '3', name: 'Shopping', taskCount: 2 },
  ];

  const mockTasks = [
    { id: '1', name: 'Complete project proposal', listId: '1', listName: 'Work Tasks' },
    { id: '2', name: 'Review design mockups', listId: '1', listName: 'Work Tasks' },
    { id: '3', name: 'Buy groceries', listId: '3', listName: 'Shopping' },
  ];

  // Mock conversation data
  const mockConversations = [
    { id: '1', title: 'Project Planning Discussion', lastMessage: '2 hours ago' },
    { id: '2', title: 'Task Management Help', lastMessage: '1 day ago' },
    { id: '3', title: 'Design Review Questions', lastMessage: '3 days ago' },
    { id: '4', title: 'Shopping List Organization', lastMessage: '1 week ago' },
  ];

  return (
    <aside
      ref={sidebarRef}
      className={cn(
        "fixed right-0 top-0 z-40 h-full bg-background border-l border-border/50 transition-all duration-300 ease-out",
        isOpen ? "" : "overflow-hidden"
      )}
      style={{
        width: isOpen ? `${width}px` : '0px',
        transition: 'width 300ms cubic-bezier(0.4, 0, 0.2, 1)',
      }}
    >
      {/* Resize Handle */}
      {isOpen && (
        <div
          className={cn(
            "absolute left-0 top-0 w-1 h-full cursor-ew-resize bg-transparent hover:bg-border/50 transition-colors duration-200 z-50",
            isResizing && "bg-border"
          )}
          onMouseDown={handleMouseDown}
          title="Drag to resize"
        />
      )}
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between px-4 pt-4 pb-2">
          <div className="flex items-center gap-2">
            <NIcon className="h-5 w-5 flex-shrink-0" />
            <h2 className="font-medium">Agent</h2>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Second Header Row - Conversations and New Chat */}
        <div className="flex items-center justify-between px-4 pb-4">
          {/* Left: Hamburger Menu aligned with N icon center */}
          <div className="flex items-center">
            <div className="w-5 flex justify-center">
              <Popover open={isConversationsOpen} onOpenChange={setIsConversationsOpen}>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <Menu className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-0" align="start">
                  <div className="p-3">
                    {/* Header with New Thread and Search */}
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-medium text-sm">Conversations</h3>
                      <div className="flex items-center gap-1">
                        <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                          <Search className="h-3 w-3" />
                        </Button>
                        <Button variant="ghost" size="sm" className="h-7 px-2 text-xs">
                          <Plus className="h-3 w-3 mr-1" />
                          New thread
                        </Button>
                      </div>
                    </div>

                    {/* Conversations List */}
                    <div className="space-y-1 max-h-64 overflow-y-auto">
                      {mockConversations.map((conversation) => (
                        <button
                          key={conversation.id}
                          type="button"
                          className="w-full text-left px-2 py-2 hover:bg-accent/20 rounded text-sm"
                        >
                          <div className="font-medium truncate">{conversation.title}</div>
                          <div className="text-xs text-muted-foreground">{conversation.lastMessage}</div>
                        </button>
                      ))}
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>
          {/* Right: New Chat Button */}
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Plus className="h-4 w-4" />
          </Button>
        </div>

        {/* Messages */}
        <div
          className="flex-1 overflow-y-auto p-4 space-y-4 transition-opacity duration-200"
          style={{
            opacity: isOpen ? 1 : 0,
            transitionDelay: isOpen ? '150ms' : '0ms',
          }}
        >
          {messages.map((message) => (
            <div
              key={message.id}
              className={cn(
                "flex flex-col gap-1",
                message.isUser ? "items-end" : "items-start"
              )}
            >
              <div
                className={cn(
                  "max-w-[80%] rounded-lg px-3 py-2 text-sm relative overflow-hidden whitespace-pre-wrap break-words",
                  message.isUser
                    ? "text-foreground"
                    : "text-foreground"
                )}
                style={message.isUser ? {
                  background: `
                    linear-gradient(135deg,
                      color-mix(in srgb, oklch(0.5 0.25 240) 15%, transparent) 0%,
                      color-mix(in srgb, oklch(0.5 0.25 270) 15%, transparent) 100%
                    )
                  `,
                  backdropFilter: 'blur(8px) saturate(1.1)',
                  WebkitBackdropFilter: 'blur(8px) saturate(1.1)',
                  border: '1px solid color-mix(in srgb, oklch(0.5 0.25 240) 25%, transparent)',
                  boxShadow: `
                    0 1px 3px rgba(0, 0, 0, 0.1),
                    0 1px 2px rgba(0, 0, 0, 0.06),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1)
                  `
                } : {
                  background: 'rgba(255, 255, 255, 0.08)',
                  backdropFilter: 'blur(12px) saturate(1.2)',
                  WebkitBackdropFilter: 'blur(12px) saturate(1.2)',
                  border: '1px solid rgba(255, 255, 255, 0.15)',
                  boxShadow: `
                    0 1px 3px rgba(0, 0, 0, 0.1),
                    0 1px 2px rgba(0, 0, 0, 0.06),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1)
                  `
                }}
              >
                {message.text}
              </div>
              <span className="text-xs text-muted-foreground">
                {formatTime(message.timestamp)}
              </span>
            </div>
          ))}
          
          {/* Typing indicator */}
          {isTyping && (
            <div className="flex items-start gap-1">
              <div
                className="rounded-lg px-3 py-2 text-sm text-foreground relative overflow-hidden"
                style={{
                  background: 'rgba(255, 255, 255, 0.08)',
                  backdropFilter: 'blur(12px) saturate(1.2)',
                  WebkitBackdropFilter: 'blur(12px) saturate(1.2)',
                  border: '1px solid rgba(255, 255, 255, 0.15)',
                  boxShadow: `
                    0 1px 3px rgba(0, 0, 0, 0.1),
                    0 1px 2px rgba(0, 0, 0, 0.06),
                    inset 0 1px 0 rgba(255, 255, 255, 0.1)
                  `
                }}
              >
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                  <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                  <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Chat Input and Controls Container */}
        <div
          className="p-4 transition-opacity duration-200"
          style={{
            opacity: isOpen ? 1 : 0,
            transitionDelay: isOpen ? '150ms' : '0ms',
          }}
        >
          {/* Bordered Container for Input and Controls */}
          <div className="border border-border rounded-lg p-3 space-y-2">
            {/* Input Field */}
            <div>
              <Textarea
                ref={inputRef}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Ask or instruct NeoTask Agent..."
                className="w-full min-h-[40px] max-h-[120px] resize-none border-none focus:ring-0 focus:outline-none focus:border-none focus-visible:ring-0 focus-visible:outline-none focus-visible:border-none shadow-none bg-transparent p-0"
                rows={1}
                style={{ outline: 'none', border: 'none', boxShadow: 'none' }}
              />
            </div>

          {/* First Row: Auto, Question, Model, and Action Buttons */}
          <div className="flex items-center justify-between gap-1">
            {/* Left side: Auto, Question, Model */}
            <div className="flex items-center gap-1">
              {/* Auto Toggle */}
              <Toggle
                pressed={isAutoMode}
                onPressedChange={setIsAutoMode}
                size="sm"
                className="h-7 px-2 text-xs"
              >
                Auto
              </Toggle>

              {/* Ask Question Toggle with Message Circle Icon */}
              <Toggle
                pressed={isQuestionMode}
                onPressedChange={setIsQuestionMode}
                size="sm"
                className="h-7 w-7 p-0"
              >
                <MessageCircle className="h-4 w-4" />
              </Toggle>

              {/* Vertical Divider */}
              <div className="h-4 w-px bg-border mx-1" />

              {/* AI Model Selection with Switch Icon - styled like tags button */}
              <Select value={selectedModel} onValueChange={setSelectedModel}>
                <SelectTrigger className="flex items-center gap-2 text-muted-foreground hover:!text-foreground hover:!bg-muted/50 transition-colors rounded-lg h-7 px-2 border-none bg-transparent focus:ring-0 shadow-none w-auto cursor-pointer">
                  <GitBranch className="h-4 w-4" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="gpt-4">GPT-4</SelectItem>
                  <SelectItem value="gpt-3.5">GPT-3.5</SelectItem>
                  <SelectItem value="claude">Claude</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Right side: Action Buttons */}
            <div className="flex items-center gap-1">
              {/* Paperclip - File Attachment */}
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                <Paperclip className="h-3 w-3" />
              </Button>
              {/* Magic - Enhance Prompt */}
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                <Wand2 className="h-4 w-4" />
              </Button>
              {/* Send Button */}
              <Button
                onClick={handleSendMessage}
                disabled={!inputValue.trim() || isTyping}
                size="sm"
                className="px-2 h-7"
              >
                <Send className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {/* Second Row: @ Menu, Selected Tags, Memories, Rules */}
          <div className="flex items-center gap-1 flex-wrap">
              {/* @ - Lists/Tasks Menu */}
              <Popover open={isListsMenuOpen} onOpenChange={setIsListsMenuOpen}>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                    <AtSign className="h-3 w-3" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-64 p-2" align="start">
                  <div className="space-y-2">
                    <div className="font-medium text-sm">Lists</div>
                    {mockLists.map((list) => (
                      <button
                        key={list.id}
                        type="button"
                        onClick={() => {
                          addTag({ id: list.id, type: 'list', name: list.name });
                          setIsListsMenuOpen(false);
                        }}
                        className="w-full text-left px-2 py-1 hover:bg-accent/20 rounded text-sm flex justify-between"
                      >
                        <span>{list.name}</span>
                        <span className="text-muted-foreground text-xs">{list.taskCount}</span>
                      </button>
                    ))}

                    <div className="font-medium text-sm mt-3">Tasks</div>
                    {mockTasks.map((task) => (
                      <button
                        key={task.id}
                        type="button"
                        onClick={() => {
                          addTag({
                            id: task.id,
                            type: 'task',
                            name: task.name,
                            listName: task.listName
                          });
                          setIsListsMenuOpen(false);
                        }}
                        className="w-full text-left px-2 py-1 hover:bg-accent/20 rounded text-sm"
                      >
                        <div className="text-xs text-muted-foreground">{task.listName}</div>
                        <div>{task.name}</div>
                      </button>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>

              {/* Brain - Memories */}
              <Popover open={isMemoriesOpen} onOpenChange={setIsMemoriesOpen}>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                    <Brain className="h-3 w-3" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-48 p-2" align="start">
                  <div className="text-sm">
                    <div className="font-medium mb-2">Memories</div>
                    <div className="text-muted-foreground text-xs">
                      Access your saved memories and context
                    </div>
                  </div>
                </PopoverContent>
              </Popover>

              {/* Book - Rules & Guidelines */}
              <Popover open={isRulesOpen} onOpenChange={setIsRulesOpen}>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                    <BookOpen className="h-3 w-3" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-48 p-2" align="start">
                  <div className="text-sm">
                    <div className="font-medium mb-2">Rules & Guidelines</div>
                    <div className="text-muted-foreground text-xs">
                      Configure AI behavior and guidelines
                    </div>
                  </div>
                </PopoverContent>
              </Popover>

              {/* Selected Tags Display - positioned after all icons */}
              {selectedTags.map((tag) => (
                <div
                  key={`${tag.type}-${tag.id}`}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-accent/20 text-accent-foreground rounded-md text-xs"
                >
                  <span>
                    {tag.type === 'task' && tag.listName ? `${tag.listName}: ${tag.name}` : tag.name}
                  </span>
                  <button
                    type="button"
                    onClick={() => removeTag(tag.id, tag.type)}
                    className="hover:bg-accent/30 rounded-sm p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>

          </div>
          </div>
        </div>
    </aside>
  );
}
